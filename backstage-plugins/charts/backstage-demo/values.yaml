# Default values for backstage-demo.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# Backstage configuration (passed to backstage dependency)
backstage:
  fullnameOverride: "backstage-demo"
  backstage:
    image:
      registry: "ghcr.io"
      repository: "openchoreo/backstage-demo"
      tag: "0.0.0-latest-dev"
      pullPolicy: IfNotPresent

    # Resource limits and requests
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi

    # Extra environment variables
    extraEnvVars:
      - name: OPENCHOREO_API_URL
        value: "http://api-server.openchoreo-control-plane.svc.cluster.local:8080/api/v1"
      - name: OPENCHOREO_OBS_URL
        value: "http://observer.openchoreo-observability-plane.svc.cluster.local:8080"
      - name: BACKEND_SECRET
        valueFrom:
          secretKeyRef:
            name: backstage-secrets
            key: backend-secret

# Backend authentication secret (leave empty to auto-generate)
# backendSecret: "your-custom-backend-secret"
