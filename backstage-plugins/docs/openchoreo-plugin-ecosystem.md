## OpenChoreo Plugin Ecosystem for Backstage

This document provides a comprehensive reference for the OpenChoreo-related plugins and supporting libraries in this Backstage workspace. It covers the complete plugin inventory, architecture, integration strategy, relationships, and implementation details to help developers understand, operate, and extend the ecosystem.

---

### 1) Complete Plugin Inventory

Below is the inventory of all plugins under plugins/.

- Plugin: @internal/plugin-catalog-backend-module-openchoreo
  - Type: Backend plugin module (catalog)
  - Purpose: Discover OpenChoreo resources and ingest them as Backstage catalog entities (Domain, System, Component, API)
  - Key dependencies/tech: @backstage/backend-plugin-api, @backstage/plugin-catalog-node, @backstage/catalog-model, @backstage/config, @internal/plugin-openchoreo-api
  - Main functionality/capabilities:
    - Scheduled entity provider that fetches Organizations, Projects, Components from OpenChoreo
    - Translates to Domain/System/Component entities, and API entities for Service endpoints
    - Annotates entities with openchoreo.io/* labels

- Plugin: @internal/plugin-scaffolder-backend-module-openchoreo
  - Type: Backend plugin module (scaffolder)
  - Purpose: Provide scaffolder actions to create OpenChoreo projects and components
  - Key dependencies/tech: @backstage/backend-plugin-api, @backstage/plugin-scaffolder-node, @backstage/config, @internal/plugin-openchoreo-api
  - Main functionality/capabilities:
    - Actions: openchoreo:project:create, openchoreo:component:create
    - Supports built-in OpenChoreo CI (build template selection, parameters) or external CI
    - Emits outputs consumable by later steps in templates

- Plugin: @internal/plugin-openchoreo-api
  - Type: Node library (shared)
  - Purpose: Typed client library for calling OpenChoreo APIs and modeling responses
  - Key dependencies/tech: cross-fetch, uri-template, @backstage/config, @backstage/backend-plugin-api
  - Main functionality/capabilities:
    - DefaultApiClient for REST endpoints (orgs, projects, components, builds, build-templates, workloads, bindings)
    - ObservabilityApiClient for runtime/build logs (with observer URL discovery and caching)
    - OpenChoreoApiClient providing higher-level operations with logging and token propagation
    - Rich TypeScript models for Projects, Organizations, Components, Workloads, Builds, Bindings

- Plugin: @internal/plugin-choreo-backend
  - Type: Backend plugin (service API)
  - Purpose: Provide REST endpoints to the frontend for environments, builds, cell diagram, runtime logs, workload management
  - Key dependencies/tech: express, @internal/plugin-openchoreo-api, @backstage/backend-plugin-api, @backstage/config
  - Main functionality/capabilities:
    - Routes for deployments, promotion, binding updates, cell-diagram, build templates, builds (list/trigger), component details, build logs, runtime logs, workload get/apply

- Plugin: @internal/plugin-choreo
  - Type: Frontend plugin (UI)
  - Purpose: UI surfaces for OpenChoreo data on Backstage entity pages (Environments, Cell Diagram, Runtime Logs, Builds)
  - Key dependencies/tech: @backstage/core-plugin-api, @backstage/catalog-model, React, @internal/plugin-openchoreo-api (types), @wso2/cell-diagram
  - Main functionality/capabilities:
    - Routable extensions to component/system pages
    - Calls choreo-backend endpoints using DiscoveryApi/IdentityApi
    - Renders environment status, cell diagrams, builds, runtime logs

---

### 2) Architecture Overview

#### Integration with Backstage
- Backend registration (packages/backend/src/index.ts):
  - Adds choreo-backend (API service)
  - Adds catalog-backend-module-openchoreo (entity provider)
  - Adds scaffolder-backend-module-openchoreo (scaffolder actions)
- Frontend: choreo plugin exposes routable extensions for catalog entity pages.

#### Inter-plugin dependencies and relationships
- openchoreo-api (library) is the foundation used by:
  - catalog-backend-module-openchoreo (for discovery)
  - scaffolder-backend-module-openchoreo (for create operations)
  - choreo-backend (for serving UI data)
- choreo-backend provides REST endpoints consumed by choreo frontend
- catalog-backend-module-openchoreo outputs entities used by the catalog and UI

#### Data Flow
- Discovery path:
  1) OpenChoreo services → openchoreo-api → catalog-backend-module-openchoreo → Backstage catalog (Domain/System/Component/API)
- Scaffolding path:
  1) Backstage templates → scaffolder-backend-module-openchoreo (actions) → openchoreo-api → OpenChoreo services
- UI path:
  1) Catalog entities (with openchoreo annotations) → choreo (frontend) → choreo-backend → openchoreo-api → OpenChoreo services → JSON → choreo renders

---

### 3) OpenChoreo Integration Strategy

#### End-to-End Pattern
- Library: `@internal/plugin-openchoreo-api` encapsulates HTTP, models, and auth token support.
- Catalog: `@internal/plugin-catalog-backend-module-openchoreo` creates and syncs catalog entities.
- Scaffolder: `@internal/plugin-scaffolder-backend-module-openchoreo` offers actions used by templates to provision OpenChoreo resources.
- Backend UI API: `@internal/plugin-choreo-backend` exposes REST endpoints tailored for the UI.
- Frontend UI: `@internal/plugin-choreo` displays the data on Backstage entity pages.

#### Entity Mapping Strategy
- OpenChoreo Organization → Backstage Domain
- OpenChoreo Project → Backstage System
- OpenChoreo Component → Backstage Component
- Service Component Endpoints → Backstage API

Key annotations/labels used on entities (examples):
- `openchoreo.io/organization`, `openchoreo.io/project`, `openchoreo.io/component-id`, `openchoreo.io/status`, `openchoreo.io/endpoint-*`

#### Configuration & Setup

Add to app-config.yaml:

```yaml
openchoreo:
  baseUrl: http://localhost:8080
  # token: ${OPENCHOREO_TOKEN}
  schedule:
    frequency: 30  # seconds
    timeout: 120   # seconds

catalog:
  locations:
    - type: file
      target: ../../templates/create-openchoreo-project/template.yaml
      rules:
        - allow: [Template]
    - type: file
      target: ../../templates/create-openchoreo-component/template.yaml
      rules:
        - allow: [Template]
```

Backend registration (packages/backend/src/index.ts):

```ts
backend.add(import('@internal/plugin-choreo-backend'));
backend.add(import('@internal/plugin-catalog-backend-module-openchoreo'));
backend.add(import('@internal/plugin-scaffolder-backend-module-openchoreo'));
```

Templates usage:
- Project template uses `openchoreo:project:create`
- Component template uses `openchoreo:component:create` with built-in CI options

---

### 4) Plugin Relationships

- Consumers of openchoreo-api:
  - catalog-backend-module-openchoreo
  - scaffolder-backend-module-openchoreo
  - choreo-backend
- Frontend↔Backend communication:
  - choreo (frontend) → choreo-backend (REST) via DiscoveryApi/IdentityApi
- Catalog integration:
  - catalog-backend-module-openchoreo → catalog DB (entities) → used by UI
- Scaffolder integration:
  - templates → scaffolder-backend-module-openchoreo actions → OpenChoreo API

High-level mapping:
- choreo (frontend) depends on: choreo-backend (runtime) and catalog entities
- choreo-backend depends on: openchoreo-api (compile/runtime)
- catalog-backend-module-openchoreo depends on: openchoreo-api
- scaffolder-backend-module-openchoreo depends on: openchoreo-api

---

### 5) Technical Implementation Details

#### Key Classes & Functions
- openchoreo-api
  - DefaultApiClient: low-level REST client (projectsGet, organizationsGet, componentsGet, projectsPost, componentsPost, builds*, bindings*, workloads*)
  - ObservabilityApiClient: fetches runtime/build logs; caches observer URLs
  - OpenChoreoApiClient: logger-aware high-level client (getAllOrganizations, getAllProjects, getAllComponents, createProject, createComponent, getComponent, getWorkload, updateWorkload, getComponentBindings, updateComponentBinding, promoteComponent, getAllBuildTemplates, getAllBuilds, triggerBuild)
  - Factory: createOpenChoreoApiClient(config, logger)
- catalog-backend-module-openchoreo
  - OpenChoreoEntityProvider: scheduled entity provider
    - translateOrganizationToDomain, translateProjectToEntity, translateComponentToEntity
    - translateServiceComponentToEntity, createApiEntitiesFromWorkload
- scaffolder-backend-module-openchoreo
  - Actions: createProjectAction (openchoreo:project:create), createComponentAction (openchoreo:component:create)
  - Extracts entity refs (domain:default/*, system:default/*) to org/project names
- choreo-backend
  - Router endpoints: /deploy, /promote-deployment, /update-binding, /cell-diagram, /build-templates, /builds (GET/POST), /component, /build-logs, /logs/component/:componentId (POST), /workload (GET/POST)
  - Services: EnvironmentInfoService, BuildTemplateInfoService, BuildInfoService, ComponentInfoService, RuntimeLogsInfoService, WorkloadService
- choreo (frontend)
  - Routable extensions: Environments, CellDiagram, RuntimeLogs
  - Uses IdentityApi for auth; DiscoveryApi for backend URL resolution

#### API Endpoints (choreo-backend)
- GET /deploy → environmentInfoService.fetchDeploymentInfo
- POST /promote-deployment → environmentInfoService.promoteComponent
- PATCH /update-binding → environmentInfoService.updateComponentBinding
- GET /cell-diagram → cellDiagramInfoService.fetchProjectInfo
- GET /build-templates → buildTemplateInfoService.fetchBuildTemplates
- GET /builds → buildInfoService.fetchBuilds
- POST /builds → buildInfoService.triggerBuild
- GET /component → componentInfoService.fetchComponentDetails
- GET /build-logs → buildInfoService.fetchBuildLogs
- POST /logs/component/:componentId → runtimeLogsInfoService.fetchRuntimeLogs
- GET /workload → workloadInfoService.fetchWorkloadInfo
- POST /workload → workloadInfoService.applyWorkload

#### Data Models (selected)
- ModelsOrganization { name, displayName, description, namespace, createdAt, status }
- ModelsProject { name, orgName, displayName, description, deploymentPipeline?, createdAt, status }
- ModelsComponent { name, description, type, projectName, orgName, repositoryUrl, branch, createdAt, status, buildConfig? }
- ModelsWorkload { name, type, owner, containers?, endpoints?, connections?, status?, createdAt? }
- WorkloadEndpoint { type: 'TCP'|'UDP'|'HTTP'|'REST'|'gRPC'|'Websocket'|'GraphQL', port, schema? }

#### Configuration Schema
- openchoreo-api reads:
  - openchoreo.baseUrl (string)
  - openchoreo.token? (string)
- catalog module reads:
  - openchoreo.schedule.frequency? (seconds, default 30)
  - openchoreo.schedule.timeout? (seconds, default 120)

Example:
```yaml
openchoreo:
  baseUrl: http://localhost:8080
  token: ${OPENCHOREO_TOKEN}
  schedule:
    frequency: 30
    timeout: 120
```

---

### Diagrams

Flow of data and control:

```mermaid
graph TD
  subgraph OpenChoreo Platform
    OCAPI[OpenChoreo REST APIs]
  end

  subgraph Shared Library
    OCA[@internal/plugin-openchoreo-api]
  end

  subgraph Backend
    CBE[@internal/plugin-catalog-backend-module-openchoreo]
    SBE[@internal/plugin-scaffolder-backend-module-openchoreo]
    CHB[@internal/plugin-choreo-backend]
  end

  subgraph Frontend
    CHF[@internal/plugin-choreo]
  end

  OCAPI <--> OCA
  OCA --> CBE
  OCA --> SBE
  OCA --> CHB
  CBE -->|entities| Catalog[(Backstage Catalog)]
  CHF -->|REST| CHB
  CHF -->|reads| Catalog
```

---

### Extensibility Notes
- To add new OpenChoreo operations:
  - Extend DefaultApiClient and OpenChoreoApiClient with new endpoints/models
  - Add new services and handlers in choreo-backend (if UI needs it)
  - Update catalog mapping logic if new resource types should appear as entities
  - Consider new scaffolder actions for new provisioning workflows

- To adjust entity mapping:
  - Update OpenChoreoEntityProvider translation helpers and annotations
  - Ensure frontend constants (CHOREO_LABELS) match annotations

---

This guide should equip developers to understand, use, and extend the OpenChoreo plugin ecosystem within Backstage.

