name: Helm Chart Release

on:
  push:
    tags:
      - 'v*.*.*'

env:
  CHART_PATH: charts/backstage-demo
  REGISTRY: ghcr.io
  CHART_REGISTRY: oci://ghcr.io/openchoreo

jobs:
  validate-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Helm
        uses: azure/setup-helm@v4
        with:
          version: 'v3.12.0'

      - name: Add Helm repositories
        run: |
          helm repo add backstage https://backstage.github.io/charts
          helm repo add bitnami https://charts.bitnami.com/bitnami
          helm repo update

      - name: Build chart dependencies
        run: |
          helm dependency build ${{ env.CHART_PATH }}

      - name: Lint <PERSON><PERSON> chart
        run: |
          helm lint ${{ env.CHART_PATH }}

      - name: Validate chart templates
        run: |
          helm template test-release ${{ env.CHART_PATH }} --dry-run

  release:
    needs: validate-and-test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up <PERSON><PERSON>
        uses: azure/setup-helm@v4
        with:
          version: 'v3.12.0'

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Determine version
        id: version
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            # Read version from VERSION file or extract from git tag
            if [[ -f "VERSION" ]]; then
              VERSION=$(cat VERSION)
            else
              VERSION="${GITHUB_REF#refs/tags/v}"
            fi
          fi
          echo "VERSION=${VERSION}" >> $GITHUB_OUTPUT
          echo "APP_VERSION=${VERSION}" >> $GITHUB_OUTPUT

      - name: Update Chart.yaml versions (temporary)
        run: |
          VERSION="${{ steps.version.outputs.VERSION }}"
          APP_VERSION="${{ steps.version.outputs.APP_VERSION }}"
          
          # Temporarily update chart version and appVersion in Chart.yaml (not committed)
          sed -i "s/0.0.0-latest-dev/${VERSION}/" ${{ env.CHART_PATH }}/Chart.yaml
          sed -i "s/latest-dev/${APP_VERSION}/" ${{ env.CHART_PATH }}/Chart.yaml
          
          # Update image tag in values.yaml to match the Docker image version (without 'v' prefix)
          sed -i "s/tag: \".*\"/tag: \"${APP_VERSION}\"/" ${{ env.CHART_PATH }}/values.yaml

      - name: Package Helm chart
        run: |
          helm dependency update ${{ env.CHART_PATH }}
          helm package ${{ env.CHART_PATH }} --destination ./charts-package

      - name: Push Helm chart to OCI registry
        run: |
          VERSION="${{ steps.version.outputs.VERSION }}"
          CHART_PACKAGE="./charts-package/backstage-demo-${VERSION}.tgz"
          helm push "${CHART_PACKAGE}" ${{ env.CHART_REGISTRY }}

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            ./charts-package/*.tgz
          generate_release_notes: true
          prerelease: ${{ contains(steps.version.outputs.VERSION, '-') }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
