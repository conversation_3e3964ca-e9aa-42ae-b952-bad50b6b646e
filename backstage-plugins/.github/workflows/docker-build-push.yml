name: Build and Push Multi-arch Docker Image

on:
  push:
    branches:
      - main
    tags:
      - 'v*.*.*'
  pull_request:
    branches:
      - main
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: openchoreo/backstage-plugins

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'yarn'

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Determine image tag
        id: tag
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            # Extract tag and remove 'v' prefix if present
            TAG=${GITHUB_REF#refs/tags/}
            echo "IMAGE_TAG=${TAG#v}" >> $GITHUB_OUTPUT
          elif [[ $GITHUB_REF == refs/heads/main ]]; then
            echo "IMAGE_TAG=latest" >> $GITHUB_OUTPUT
          else
            echo "IMAGE_TAG=${GITHUB_SHA::8}" >> $GITHUB_OUTPUT
          fi

      - name: Build and push image using script
        if: github.event_name != 'pull_request'
        env:
          IMAGE_NAME: ${{ env.IMAGE_NAME }}
          IMAGE_TAG: ${{ steps.tag.outputs.IMAGE_TAG }}
          REGISTRY: ${{ env.REGISTRY }}
        run: |
          chmod +x scripts/build-and-push-no-tsc.sh
          ./scripts/build-and-push-no-tsc.sh

      - name: Build image only (PR)
        if: github.event_name == 'pull_request'
        env:
          IMAGE_NAME: ${{ env.IMAGE_NAME }}
          IMAGE_TAG: pr-${{ github.event.number }}
          REGISTRY: ""
        run: |
          chmod +x scripts/build-and-push-no-tsc.sh
          ./scripts/build-and-push-no-tsc.sh
